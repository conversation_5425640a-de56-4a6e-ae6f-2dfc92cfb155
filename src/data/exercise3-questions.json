{"metadata": {"exerciseId": 3, "type": "quiz_questions", "version": "1.0.0", "description": "Quiz questions covering fundamental story point estimation principles"}, "questions": [{"id": "relative-sizing", "question": "Story points are about relative sizing, not absolute time estimation.", "answer": true, "explanation": "Correct! Story points compare the relative effort/complexity between stories, not predict exact time. This makes them more reliable than time estimates.", "category": "Core Concept"}, {"id": "fibonacci-sequence", "question": "The <PERSON><PERSON><PERSON><PERSON> sequence (1, 2, 3, 5, 8, 13...) is used because it reflects the increasing uncertainty in larger estimates.", "answer": true, "explanation": "Exactly! As stories get larger, our uncertainty grows exponentially. Fi<PERSON><PERSON><PERSON> gaps reflect this natural uncertainty progression.", "category": "Estimation Scale"}, {"id": "time-estimation", "question": "A 5-point story should always take exactly 5 times longer than a 1-point story.", "answer": false, "explanation": "Not necessarily! Story points reflect relative effort/complexity, but actual time can vary based on developer experience, interruptions, and other factors.", "category": "Common Misconception"}, {"id": "three-factors", "question": "The three main factors in story point estimation are complexity, effort, and uncertainty.", "answer": true, "explanation": "Perfect! These three factors work together: complexity (how hard to understand/implement), effort (amount of work), and uncertainty (how much we don't know).", "category": "Estimation Factors"}, {"id": "team-velocity", "question": "Team velocity should remain constant across all sprints.", "answer": false, "explanation": "Velocity naturally fluctuates due to team changes, learning, holidays, and other factors. It's a planning tool, not a performance metric.", "category": "Velocity & Planning"}, {"id": "individual-estimation", "question": "Story points should be estimated by individual developers working alone.", "answer": false, "explanation": "Team estimation (like Planning Poker) is more accurate because it combines different perspectives and catches assumptions that individuals might miss.", "category": "Team Process"}, {"id": "story-splitting", "question": "If a story is estimated at 21 points, it should be split into smaller stories.", "answer": true, "explanation": "Correct! Stories larger than 13 points are usually too big and uncertain. Splitting them reveals hidden complexity and improves estimation accuracy.", "category": "Story Management"}, {"id": "velocity-comparison", "question": "You can directly compare velocity between different teams to measure productivity.", "answer": false, "explanation": "Never compare velocities between teams! Each team's points are relative to their context, skills, and definition of done. It's like comparing different currencies.", "category": "Common Mistake"}]}